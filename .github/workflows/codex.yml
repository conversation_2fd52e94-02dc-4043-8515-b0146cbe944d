name: Codex

on:
  issues:
    types: [opened, labeled]
  pull_request:
    branches: [main]
    types: [labeled]

jobs:
  codex:
    # This `if` check provides complex filtering logic to avoid running Codex
    # on every PR. Admittedly, one thing this does not verify is whether the
    # sender has write access to the repo: that must be done as part of a
    # runtime step.
    #
    # Note the label values should match the ones in the .github/codex/labels
    # folder.
    if: |
      (github.event_name == 'issues' && (
        (github.event.action == 'labeled' && (github.event.label.name == 'codex-attempt' || github.event.label.name == 'codex-triage'))
      )) ||
      (github.event_name == 'pull_request' && github.event.action == 'labeled' && github.event.label.name == 'codex-review')
    runs-on: ubuntu-latest
    permissions:
      contents: write # can push or create branches
      issues: write # for comments + labels on issues/PRs
      pull-requests: write # for PR comments/labels
    steps:
      # TODO: Consider adding an optional mode (--dry-run?) to actions/codex
      # that verifies whether Codex should actually be run for this event.
      # (For example, it may be rejected because the sender does not have
      # write access to the repo.) The benefit would be two-fold:
      # 1. As the first step of this job, it gives us a chance to add a reaction
      #    or comment to the PR/issue ASAP to "ack" the request.
      # 2. It saves resources by skipping the clone and setup steps below if
      #    Codex is not going to run.

      - name: Checkout repository
        uses: actions/checkout@v4

      # We install the dependencies like we would for an ordinary CI job,
      # particularly because Codex will not have network access to install
      # these dependencies.
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.8.1
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "store_path=$(pnpm store path --silent)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.store_path }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - uses: dtolnay/rust-toolchain@1.87
        with:
          targets: x86_64-unknown-linux-gnu
          components: clippy

      - uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            ${{ github.workspace }}/codex-rs/target/
          key: cargo-ubuntu-24.04-x86_64-unknown-linux-gnu-${{ hashFiles('**/Cargo.lock') }}

      # Note it is possible that the `verify` step internal to Run Codex will
      # fail, in which case the work to setup the repo was worthless :(
      - name: Run Codex
        uses: ./.github/actions/codex
        with:
          openai_api_key: ${{ secrets.CODEX_OPENAI_API_KEY }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          codex_home: ./.github/codex/home
