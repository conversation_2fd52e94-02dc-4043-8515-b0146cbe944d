import type { AppConfig } from "./utils/config";

import { SinglePassApp } from "./components/singlepass-cli-app";
import { render } from "ink";
import React from "react";

/**
 * 该方法用于运行单次的 CLI 应用程序，接收用户输入的提示、配置和根路径，并渲染 SinglePassApp 组件。
 * 当应用程序退出时，返回一个 Promise，以便调用者可以处理后续操作。
 */
export async function runSinglePass({
  originalPrompt, // 用户输入的原始提示，类型为可选字符串
  config, // 应用程序的配置，类型为 AppConfig
  rootPath, // 应用程序的根路径，类型为字符串
}: {
  originalPrompt?: string; // 定义 originalPrompt 为可选参数
  config: AppConfig; // 定义 config 为必需参数，类型为 AppConfig
  rootPath: string; // 定义 rootPath 为必需参数，类型为字符串
}): Promise<void> { // 返回一个 Promise，表示该方法是异步的
  return new Promise((resolve) => { // 创建一个新的 Promise
    render( // 渲染 SinglePassApp 组件
      <SinglePassApp
        originalPrompt={originalPrompt} // 将原始提示传递给组件
        config={config} // 将配置传递给组件
        rootPath={rootPath} // 将根路径传递给组件
        onExit={() => resolve()} // 当组件退出时，调用 resolve() 以解决 Promise
      />,
    );
  });
}

export default {};
