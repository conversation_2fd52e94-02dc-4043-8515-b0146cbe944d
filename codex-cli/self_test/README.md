# Self Test Directory Structure

This directory contains self-tests for the codex-cli project, organized by functional categories.

## Directory Structure

```
self_test/
├── core/                           # Core functionality tests
│   ├── simple-test.js             # Basic functionality validation
│   └── agent-loop-test.js         # Agent loop functionality tests
│
├── debug/                          # Debug-related tests
│   ├── debug-basic-test.js        # Basic debugging functionality
│   └── debug-advanced-test.js     # Advanced debugging features
│
└── network/                        # Network and proxy tests
    ├── proxy-config-test.js       # Proxy configuration tests
    └── proxy-simple-test.js       # Simple proxy functionality tests
```

## Running Tests

### Run all self-tests
```bash
# From codex-cli directory
npm run test:self
```

### Run specific category tests
```bash
# Core functionality tests
node self_test/core/simple-test.js
node self_test/core/agent-loop-test.js

# Debug tests
node self_test/debug/debug-basic-test.js
node self_test/debug/debug-advanced-test.js

# Network tests
node self_test/network/proxy-config-test.js
node self_test/network/proxy-simple-test.js
```

## Test Categories

### Core Tests
- **simple-test.js**: Validates basic CLI functionality and core features
- **agent-loop-test.js**: Tests the agent loop mechanism and interaction patterns

### Debug Tests
- **debug-basic-test.js**: Tests basic debugging capabilities
- **debug-advanced-test.js**: Tests advanced debugging features and logging

### Network Tests
- **proxy-config-test.js**: Tests proxy configuration and setup
- **proxy-simple-test.js**: Tests basic proxy functionality and connectivity

## Adding New Tests

When adding new self-tests:
1. Choose the appropriate category directory (core/debug/network)
2. Follow the naming convention: `*-test.js`
3. Update this README if adding a new category
4. Ensure tests are self-contained and can run independently 